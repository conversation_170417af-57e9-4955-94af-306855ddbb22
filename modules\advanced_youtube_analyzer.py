# محلل يوتيوب المتقدم مع دعم Whisper وتحليل القنوات المحددة
import requests
import json
import re
import asyncio
import aiohttp
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs, quote
from .logger import logger
from .database import db
from .whisper_quality_checker import whisper_quality_checker
from .advanced_text_analyzer import advanced_text_analyzer
from .text_cleanup_processor import text_cleanup_processor
from config.settings import google_api_manager, BotConfig

class AdvancedYouTubeAnalyzer:
    """محلل يوتيوب متقدم مع استخراج النصوص بـ Whisper والبحث في القنوات المحددة"""
    
    def __init__(self):
        self.base_url = "https://www.googleapis.com/youtube/v3"
        self.session = requests.Session()
        
        # إعدادات Whisper API
        self.whisper_api_url = "https://nanami34-ai55.hf.space/api/transcribe"
        self.whisper_api_key = "whisper-hf-spaces-2025"
        self.hf_token = "*************************************"
        
        # القنوات المحددة بالأولوية (محدثة مع المعرفات)
        self.priority_channels = [
            {
                'url': 'https://youtube.com/@saudigamer',
                'name': 'Saudi Gamer',
                'language': 'ar',
                'priority': 1,
                'id': 'UCdHyMy7pxEYUUEJYhAK7pwQ'  # تم استخراجه
            },
            {
                'url': 'https://youtube.com/@levelcapgaming',
                'name': 'LevelCap Gaming',
                'language': 'en',
                'priority': 2,
                'id': 'UClMXf2oP5UiW_V4dwHxY0Mg'  # تم استخراجه
            },
            {
                'url': 'https://youtube.com/@jorraptor',
                'name': 'JorRaptor',
                'language': 'en',
                'priority': 3,
                'id': 'UCzF5oxzeidHOZzy4KK5nxCQ'  # تم استخراجه
            },
            {
                'url': 'https://youtube.com/@gameranxtv',
                'name': 'gameranx',
                'language': 'en',
                'priority': 4,
                'id': 'UCNvzD8IgbJzYtEQu_qkoFrg'  # معرف معروف
            },
            {
                'url': 'https://youtube.com/@gamespot',
                'name': 'GameSpot',
                'language': 'en',
                'priority': 5,
                'id': 'UCbu2SsF-Or3Rsn3NxqODImw'  # معرف معروف
            },
            {
                'url': 'https://youtube.com/@ign',
                'name': 'IGN',
                'language': 'en',
                'priority': 6,
                'id': 'UCKy1dAqELo0zrOtPkf0eTMw'  # تم استخراجه
            }
        ]
        
        # كلمات مفتاحية للتعرف على أخبار الألعاب
        self.gaming_keywords = {
            'ar': [
                'أخبار', 'خبر', 'جديد', 'تحديث', 'إصدار', 'لعبة', 'ألعاب', 'تسريب', 'إعلان',
                'مراجعة', 'تقييم', 'أفضل', 'قائمة', 'توقعات', 'معاينة', 'عرض', 'تريلر',
                'شركة', 'استوديو', 'منصة', 'كونسول', 'بلايستيشن', 'إكسبوكس', 'نينتندو'
            ],
            'en': [
                'news', 'update', 'release', 'game', 'gaming', 'leak', 'announcement',
                'review', 'preview', 'trailer', 'gameplay', 'best', 'top', 'new',
                'company', 'studio', 'platform', 'console', 'playstation', 'xbox', 'nintendo'
            ]
        }
        
        # حد أقصى لمدة الفيديو (30 دقيقة كحد أقصى)
        self.max_video_duration = min(30 * 60, BotConfig.MAX_VIDEO_DURATION_MINUTES * 60)  # بالثواني
        
        # حد أقصى لعمر الفيديو (شهرين)
        self.max_video_age_days = 60
        
        # تهيئة معرفات القنوات
        self._initialize_channel_ids()
    
    def _initialize_channel_ids(self):
        """استخراج معرفات القنوات من الروابط"""
        for channel in self.priority_channels:
            if not channel['id'] and channel['url'].startswith('https://youtube.com/@'):
                try:
                    channel_id = self._extract_channel_id_from_handle(channel['url'])
                    if channel_id:
                        channel['id'] = channel_id
                        logger.info(f"✅ تم استخراج معرف القناة: {channel['name']} -> {channel_id}")
                except Exception as e:
                    logger.warning(f"⚠️ فشل في استخراج معرف القناة {channel['name']}: {e}")
    
    def _extract_channel_id_from_handle(self, channel_url: str) -> Optional[str]:
        """استخراج معرف القناة من رابط @handle مع معالجة محسنة للأخطاء"""
        try:
            # استخراج handle من الرابط
            handle = channel_url.split('@')[-1].split('?')[0]

            if not google_api_manager:
                logger.warning("⚠️ لا يمكن استخراج معرف القناة بدون Google API")
                return None

            # محاولة مع مفاتيح متعددة
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    api_key = google_api_manager.get_key()

                    # البحث عن القناة باستخدام handle
                    search_url = f"{self.base_url}/search"
                    params = {
                        'part': 'snippet',
                        'q': handle,
                        'type': 'channel',
                        'key': api_key,
                        'maxResults': 1
                    }

                    response = self.session.get(search_url, params=params, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if data.get('items'):
                            channel_id = data['items'][0]['snippet']['channelId']
                            logger.info(f"✅ تم استخراج معرف القناة: {handle} -> {channel_id}")
                            return channel_id
                        else:
                            logger.warning(f"⚠️ لم يتم العثور على القناة: {handle}")
                            return None
                    elif response.status_code == 403:
                        logger.warning(f"⚠️ خطأ 403 في استخراج معرف القناة، محاولة {attempt + 1}/{max_retries}")
                        if attempt < max_retries - 1:
                            # وضع المفتاح في القائمة السوداء
                            try:
                                google_api_manager.mark_key_failed(api_key)
                            except AttributeError:
                                # استخدام rotate_key كبديل
                                google_api_manager.rotate_key()
                            time.sleep(2)  # انتظار قبل المحاولة التالية
                            continue
                        else:
                            logger.error(f"❌ فشل في جميع محاولات استخراج معرف القناة: {handle}")
                            return None
                    else:
                        logger.error(f"❌ خطأ في استخراج معرف القناة: {response.status_code}")
                        return None

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في المحاولة {attempt + 1} لاستخراج معرف القناة: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2)
                        continue
                    else:
                        raise e

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج معرف القناة: {e}")
            return None
    
    async def find_latest_gaming_video(self) -> Optional[Dict]:
        """البحث عن أحدث فيديو ألعاب من القنوات المحددة"""
        try:
            logger.info("🔍 بدء البحث عن أحدث فيديو ألعاب من القنوات المحددة...")
            
            # ترتيب القنوات حسب الأولوية
            sorted_channels = sorted(self.priority_channels, key=lambda x: x['priority'])
            
            for channel in sorted_channels:
                if not channel['id']:
                    logger.warning(f"⚠️ تخطي القناة {channel['name']} - لا يوجد معرف")
                    continue
                
                logger.info(f"🔍 البحث في قناة: {channel['name']}")
                
                # الحصول على أحدث فيديوهات القناة
                videos = await self._get_channel_latest_videos(channel['id'], channel['language'])
                
                if not videos:
                    logger.info(f"📭 لا توجد فيديوهات مناسبة في قناة {channel['name']}")
                    continue
                
                # البحث عن أول فيديو مناسب مع فحص المدة أولاً
                for video in videos:
                    # فحص سريع للمدة قبل الفحوصات الأخرى
                    duration = await self._get_video_duration(video['id'])
                    if duration and duration > self.max_video_duration:
                        duration_minutes = duration // 60
                        logger.info(f"⏭️ تخطي فيديو طويل ({duration_minutes} دقيقة): {video['title'][:50]}...")
                        continue

                    if await self._is_suitable_gaming_video(video, channel['language']):
                        logger.info(f"✅ تم العثور على فيديو مناسب ({duration//60 if duration else '?'} دقيقة): {video['title']}")
                        video['channel_info'] = channel
                        return video
                
                logger.info(f"⏭️ لم يتم العثور على فيديو مناسب في قناة {channel['name']}")
            
            logger.warning("⚠️ لم يتم العثور على أي فيديو مناسب في جميع القنوات")
            return None
            
        except Exception as e:
            logger.error(f"❌ خطأ في البحث عن فيديو الألعاب: {e}")
            return None
    
    async def _get_channel_latest_videos(self, channel_id: str, language: str) -> List[Dict]:
        """الحصول على أحدث فيديوهات القناة مع معالجة محسنة للأخطاء"""
        try:
            if not google_api_manager:
                logger.warning("⚠️ لا يمكن الحصول على فيديوهات القناة بدون Google API")
                return []

            # محاولة مع مفاتيح متعددة
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    api_key = google_api_manager.get_key()

                    # الحصول على قائمة تشغيل الرفع للقناة
                    channel_url = f"{self.base_url}/channels"
                    channel_params = {
                        'part': 'contentDetails',
                        'id': channel_id,
                        'key': api_key
                    }

                    channel_response = self.session.get(channel_url, params=channel_params, timeout=10)

                    if channel_response.status_code == 200:
                        channel_data = channel_response.json()
                        if channel_data.get('items'):
                            uploads_playlist_id = channel_data['items'][0]['contentDetails']['relatedPlaylists']['uploads']
                            break
                        else:
                            logger.warning("⚠️ لا توجد معلومات للقناة")
                            return []
                    elif channel_response.status_code == 403:
                        logger.warning(f"⚠️ خطأ 403 - تجاوز حد API، محاولة {attempt + 1}/{max_retries}")
                        if attempt < max_retries - 1:
                            # وضع المفتاح في القائمة السوداء
                            try:
                                google_api_manager.mark_key_failed(api_key)
                            except AttributeError:
                                # استخدام rotate_key كبديل
                                google_api_manager.rotate_key()
                            await asyncio.sleep(2)  # انتظار قبل المحاولة التالية
                            continue
                        else:
                            logger.error("❌ فشل في جميع محاولات الوصول للقناة")
                            return []
                    else:
                        logger.error(f"❌ فشل في الحصول على معلومات القناة: {channel_response.status_code}")
                        return []

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في المحاولة {attempt + 1}: {e}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(2)
                        continue
                    else:
                        raise e
            
            # الحصول على أحدث فيديوهات من قائمة الرفع
            playlist_url = f"{self.base_url}/playlistItems"
            playlist_params = {
                'part': 'snippet',
                'playlistId': uploads_playlist_id,
                'key': api_key,
                'maxResults': 20,  # أحدث 20 فيديو
                'order': 'date'
            }
            
            playlist_response = self.session.get(playlist_url, params=playlist_params)
            if playlist_response.status_code != 200:
                logger.error(f"❌ فشل في الحصول على فيديوهات القناة: {playlist_response.status_code}")
                return []
            
            playlist_data = playlist_response.json()
            videos = []
            
            for item in playlist_data.get('items', []):
                video_data = {
                    'id': item['snippet']['resourceId']['videoId'],
                    'title': item['snippet']['title'],
                    'description': item['snippet']['description'],
                    'published_at': item['snippet']['publishedAt'],
                    'thumbnail': item['snippet']['thumbnails'].get('high', {}).get('url', ''),
                    'channel_id': channel_id
                }
                
                # فحص عمر الفيديو
                published_date = datetime.fromisoformat(video_data['published_at'].replace('Z', '+00:00'))
                days_old = (datetime.now(published_date.tzinfo) - published_date).days
                
                if days_old <= self.max_video_age_days:
                    videos.append(video_data)
            
            logger.info(f"📹 تم العثور على {len(videos)} فيديو حديث في القناة")
            return videos
            
        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على فيديوهات القناة: {e}")
            return []
    
    async def _is_suitable_gaming_video(self, video: Dict, language: str) -> bool:
        """فحص ما إذا كان الفيديو مناسب لأخبار الألعاب"""
        try:
            # فحص العنوان والوصف للكلمات المفتاحية
            title = video['title'].lower()
            description = video['description'].lower()
            text_to_check = f"{title} {description}"
            
            # فحص وجود كلمات مفتاحية للألعاب
            keywords = self.gaming_keywords.get(language, []) + self.gaming_keywords.get('en', [])
            has_gaming_keywords = any(keyword.lower() in text_to_check for keyword in keywords)
            
            if not has_gaming_keywords:
                logger.debug(f"⏭️ تخطي فيديو - لا يحتوي على كلمات ألعاب: {video['title']}")
                return False
            
            # فحص مدة الفيديو (أولوية قصوى)
            duration = await self._get_video_duration(video['id'])
            if duration:
                duration_minutes = duration // 60
                if duration > self.max_video_duration:
                    logger.info(f"⏭️ تخطي فيديو - مدة طويلة ({duration_minutes} دقيقة > {self.max_video_duration//60} دقيقة): {video['title']}")
                    return False
                else:
                    logger.info(f"✅ مدة الفيديو مقبولة ({duration_minutes} دقيقة): {video['title']}")
            else:
                logger.warning(f"⚠️ لا يمكن تحديد مدة الفيديو: {video['title']}")
                # في حالة عدم معرفة المدة، نرفض الفيديو للأمان
                return False
            
            # فحص ما إذا كان الفيديو تم معالجته من قبل
            if db.is_video_processed(video['id']):
                logger.debug(f"⏭️ تخطي فيديو - تم معالجته من قبل: {video['title']}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ خطأ في فحص مناسبة الفيديو: {e}")
            return False
    
    async def _get_video_duration(self, video_id: str) -> Optional[int]:
        """الحصول على مدة الفيديو بالثواني مع معالجة محسنة"""
        try:
            if not google_api_manager:
                logger.warning("⚠️ لا يمكن فحص مدة الفيديو بدون Google API")
                return None

            # محاولة مع مفاتيح متعددة
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    api_key = google_api_manager.get_key()

                    video_url = f"{self.base_url}/videos"
                    params = {
                        'part': 'contentDetails',
                        'id': video_id,
                        'key': api_key
                    }

                    response = self.session.get(video_url, params=params, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        if data.get('items') and len(data['items']) > 0:
                            duration_str = data['items'][0]['contentDetails']['duration']
                            duration_seconds = self._parse_duration(duration_str)

                            if duration_seconds > 0:
                                logger.debug(f"📏 مدة الفيديو {video_id}: {duration_seconds//60}:{duration_seconds%60:02d}")
                                return duration_seconds
                            else:
                                logger.warning(f"⚠️ مدة فيديو غير صحيحة: {duration_str}")
                                return None
                        else:
                            logger.warning(f"⚠️ لم يتم العثور على بيانات الفيديو: {video_id}")
                            return None

                    elif response.status_code == 403:
                        logger.warning(f"⚠️ مفتاح API محظور، المحاولة {attempt + 1}/{max_retries}")
                        try:
                            google_api_manager.mark_key_failed(api_key)
                        except AttributeError:
                            # استخدام rotate_key كبديل
                            google_api_manager.rotate_key()
                        continue

                    else:
                        logger.warning(f"⚠️ خطأ HTTP {response.status_code} في الحصول على مدة الفيديو")
                        return None

                except Exception as e:
                    logger.warning(f"⚠️ خطأ في المحاولة {attempt + 1}: {e}")
                    if attempt == max_retries - 1:
                        raise e
                    continue

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على مدة الفيديو {video_id}: {e}")
            return None
    
    def _parse_duration(self, duration_str: str) -> int:
        """تحويل مدة YouTube (PT1H2M3S) إلى ثواني مع معالجة محسنة"""
        try:
            if not duration_str or duration_str == 'PT':
                return 0

            # إزالة PT من البداية
            duration_str = duration_str.replace('PT', '')

            hours = 0
            minutes = 0
            seconds = 0

            # استخراج الساعات
            if 'H' in duration_str:
                hour_part = duration_str.split('H')[0]
                if hour_part.isdigit():
                    hours = int(hour_part)
                duration_str = duration_str.split('H')[1]

            # استخراج الدقائق
            if 'M' in duration_str:
                minute_part = duration_str.split('M')[0]
                if minute_part.isdigit():
                    minutes = int(minute_part)
                duration_str = duration_str.split('M')[1]

            # استخراج الثواني
            if 'S' in duration_str:
                second_part = duration_str.split('S')[0]
                if second_part.isdigit():
                    seconds = int(second_part)

            total_seconds = hours * 3600 + minutes * 60 + seconds

            # تسجيل تفصيلي للفيديوهات الطويلة
            if total_seconds > 30 * 60:  # أكثر من 30 دقيقة
                logger.info(f"⚠️ فيديو طويل: {hours}h {minutes}m {seconds}s = {total_seconds} ثانية")

            return total_seconds

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل مدة الفيديو '{duration_str}': {e}")
            return 0

    async def extract_video_transcript_with_whisper(self, video_id: str) -> Optional[str]:
        """استخراج النص من الفيديو باستخدام Whisper مع فحص الجودة والطرق البديلة"""
        try:
            logger.info(f"🎤 بدء استخراج النص من الفيديو: {video_id}")

            # الحصول على بيانات الفيديو للفحص
            video_data = await self._get_video_details(video_id)

            # الطريقة الأولى: استخدام Whisper API
            audio_url = await self._get_video_audio_url(video_id)
            if audio_url:
                transcript = await self._send_to_whisper_api(audio_url, video_id)
                if transcript and len(transcript.strip()) > 5:
                    # فحص جودة النص المستخرج مع المعالجة المحتملة
                    quality_result = await self._check_transcript_quality(transcript, video_data)

                    if quality_result['is_acceptable']:
                        # استخدم النص المحسن إذا كان متاحاً
                        final_transcript = quality_result.get('processed_transcript', transcript)
                        logger.info(f"✅ تم استخراج نص عالي الجودة من Whisper - {len(final_transcript)} حرف (جودة: {quality_result['quality_level']})")

                        # إضافة معلومات المعالجة إذا تمت
                        if quality_result.get('cleanup_applied'):
                            logger.info("🔧 تم تطبيق تحسينات على النص المستخرج")

                        return final_transcript
                    else:
                        logger.warning(f"⚠️ جودة النص من Whisper منخفضة ({quality_result['quality_level']}) - محاولة طرق بديلة...")

            # الطريقة الثانية: محاولة استخراج الترجمة المدمجة
            logger.info("🔄 محاولة استخراج الترجمة المدمجة...")
            embedded_transcript = await self._extract_embedded_captions(video_id)
            if embedded_transcript and len(embedded_transcript.strip()) > 5:
                # فحص جودة الترجمة المدمجة مع المعالجة المحتملة
                quality_result = await self._check_transcript_quality(embedded_transcript, video_data)

                if quality_result['is_acceptable']:
                    # استخدم النص المحسن إذا كان متاحاً
                    final_transcript = quality_result.get('processed_transcript', embedded_transcript)
                    logger.info(f"✅ تم استخراج ترجمة مدمجة عالية الجودة - {len(final_transcript)} حرف (جودة: {quality_result['quality_level']})")

                    # إضافة معلومات المعالجة إذا تمت
                    if quality_result.get('cleanup_applied'):
                        logger.info("🔧 تم تطبيق تحسينات على الترجمة المدمجة")

                    return final_transcript
                else:
                    logger.warning(f"⚠️ جودة الترجمة المدمجة منخفضة ({quality_result['quality_level']})")

            # الطريقة الثالثة: استخدام العنوان والوصف كبديل
            logger.info("🔄 استخدام العنوان والوصف كبديل...")
            fallback_content = await self._get_video_metadata_as_content(video_id)
            if fallback_content:
                logger.info("✅ تم استخراج محتوى بديل من البيانات الوصفية")
                return fallback_content

            logger.error("❌ فشل في استخراج أي محتوى نصي مقبول من الفيديو")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج النص: {e}")
            return None

    async def _check_transcript_quality(self, transcript: str, video_data: Dict = None) -> Dict:
        """فحص جودة النص المستخرج باستخدام النظام المتقدم مع معالجة النصوص المشكوك فيها"""
        try:
            original_transcript = transcript

            # الفحص الأساسي أولاً
            basic_quality = await whisper_quality_checker.check_transcript_quality(transcript, video_data)

            # إذا كان النص مشكوك فيه أو قصير، حاول معالجته
            if (basic_quality.get('score', 0) < 50 or
                len(transcript.split()) < 10 or
                not basic_quality.get('is_acceptable', False)):

                logger.info("🔧 النص يحتاج معالجة - بدء تنظيف وتحسين النص...")
                cleanup_result = await text_cleanup_processor.process_problematic_text(transcript, video_data)

                if cleanup_result.get('is_usable', False):
                    # استخدم النص المحسن
                    transcript = cleanup_result['processed_text']
                    logger.info(f"✅ تم تحسين النص - الطول الجديد: {len(transcript)} حرف")

                    # إعادة فحص النص المحسن
                    basic_quality = await whisper_quality_checker.check_transcript_quality(transcript, video_data)
                    basic_quality['cleanup_applied'] = True
                    basic_quality['cleanup_details'] = cleanup_result
                else:
                    logger.warning("⚠️ فشل في تحسين النص - استخدام النص الأصلي")
                    basic_quality['cleanup_attempted'] = True
                    basic_quality['cleanup_failed'] = True

            # إذا كان الفحص الأساسي مقبولاً (بعد التحسين المحتمل), قم بالتحليل المتقدم
            if basic_quality.get('is_acceptable', False):
                logger.info("🧠 بدء التحليل المتقدم للنص...")
                advanced_analysis = await advanced_text_analyzer.perform_deep_analysis(transcript, video_data)

                # دمج النتائج
                combined_score = (basic_quality['score'] * 0.4 + advanced_analysis['score'] * 0.6)

                return {
                    'score': combined_score,
                    'quality_level': advanced_analysis['quality_level'],
                    'is_acceptable': advanced_analysis['is_acceptable'],
                    'is_excellent': advanced_analysis.get('is_excellent', False),
                    'processed_transcript': transcript,
                    'original_transcript': original_transcript,
                    'basic_analysis': basic_quality,
                    'advanced_analysis': advanced_analysis,
                    'analysis_type': 'comprehensive_with_cleanup'
                }
            else:
                # إذا فشل الفحص الأساسي حتى بعد المعالجة
                logger.warning("⚠️ فشل الفحص الأساسي حتى بعد المعالجة")
                return {
                    'score': basic_quality['score'],
                    'quality_level': basic_quality['quality_level'],
                    'is_acceptable': False,
                    'processed_transcript': transcript,
                    'original_transcript': original_transcript,
                    'basic_analysis': basic_quality,
                    'analysis_type': 'basic_with_cleanup_attempt'
                }

        except Exception as e:
            logger.error(f"❌ خطأ في فحص جودة النص: {e}")
            return {
                'score': 0,
                'quality_level': 'خطأ',
                'is_acceptable': False,
                'error': str(e)
            }

    async def _get_video_details(self, video_id: str) -> Dict:
        """الحصول على تفاصيل الفيديو من YouTube API"""
        try:
            api_key = google_api_manager.get_api_key()
            if not api_key:
                logger.warning("⚠️ لا يوجد مفتاح API متاح")
                return {}

            url = f"{self.base_url}/videos"
            params = {
                'part': 'snippet,contentDetails,statistics',
                'id': video_id,
                'key': api_key
            }

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('items'):
                    video_info = data['items'][0]
                    return {
                        'id': video_id,
                        'title': video_info['snippet'].get('title', ''),
                        'description': video_info['snippet'].get('description', ''),
                        'channel_title': video_info['snippet'].get('channelTitle', ''),
                        'published_at': video_info['snippet'].get('publishedAt', ''),
                        'duration': self._parse_duration(video_info['contentDetails'].get('duration', '')),
                        'view_count': video_info['statistics'].get('viewCount', 0),
                        'like_count': video_info['statistics'].get('likeCount', 0)
                    }

            logger.warning(f"⚠️ لم يتم العثور على تفاصيل الفيديو: {video_id}")
            return {}

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على تفاصيل الفيديو: {e}")
            return {}

    async def _get_video_audio_url(self, video_id: str) -> Optional[str]:
        """الحصول على رابط الصوت من الفيديو"""
        try:
            # استخدام yt-dlp أو طريقة أخرى لاستخراج الصوت
            # هذا مثال مبسط - قد تحتاج لتطبيق أكثر تعقيداً
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # يمكن استخدام مكتبة yt-dlp هنا
            # أو استخدام خدمة خارجية لتحويل الفيديو إلى صوت

            return video_url  # مؤقتاً - سيتم تحسينه

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على رابط الصوت: {e}")
            return None

    async def _send_to_whisper_api(self, audio_source: str, video_id: str) -> Optional[str]:
        """إرسال الصوت إلى Whisper API مع معالجة محسنة للأخطاء"""
        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                logger.info(f"🎤 محاولة {attempt + 1}/{max_retries} لاستخراج النص من Whisper...")

                async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=300)) as session:
                    # إذا كان المصدر رابط فيديو، نحتاج لتحميل الصوت أولاً
                    if audio_source.startswith('http'):
                        # تحميل الصوت من الفيديو
                        audio_data = await self._download_audio_from_video(audio_source, session)
                        if not audio_data:
                            logger.warning(f"⚠️ فشل في تحميل الصوت من الفيديو - المحاولة {attempt + 1}")
                            if attempt < max_retries - 1:
                                await asyncio.sleep(retry_delay)
                                continue
                            return None

                        # محاولة إرسال الصوت إلى Whisper API
                        transcript = await self._try_whisper_api_call(session, audio_data, video_id)
                        if transcript:
                            return transcript

                        # إذا فشل Whisper، جرب طرق بديلة
                        if attempt == max_retries - 1:
                            logger.warning("⚠️ فشل Whisper في جميع المحاولات، جرب طرق بديلة...")
                            return await self._try_alternative_transcription_methods(audio_source, video_id)

                    else:
                        logger.warning("⚠️ مصدر الصوت غير صالح")
                        return None

                # تأخير بين المحاولات
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # زيادة التأخير تدريجياً

            except Exception as e:
                logger.error(f"❌ خطأ في المحاولة {attempt + 1} لـ Whisper: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    logger.error("❌ فشل في جميع محاولات Whisper")
                    return None

        return None

    async def _try_whisper_api_call(self, session: aiohttp.ClientSession, audio_data: bytes, video_id: str) -> Optional[str]:
        """محاولة استدعاء Whisper API"""
        try:
            # استخدام multipart/form-data للملفات
            data = aiohttp.FormData()
            data.add_field('file', audio_data, filename=f'{video_id}.mp3', content_type='audio/mpeg')
            data.add_field('model', 'whisper-1')
            data.add_field('language', 'auto')

            headers_for_upload = {
                'Authorization': f'Bearer {self.hf_token}',
                'X-API-Key': self.whisper_api_key
            }

            async with session.post(self.whisper_api_url, headers=headers_for_upload, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    transcript_text = result.get('text', result.get('transcription', ''))

                    if transcript_text and len(transcript_text.strip()) > 5:
                        logger.info(f"✅ تم استخراج النص بنجاح من Whisper API - {len(transcript_text)} حرف")
                        return transcript_text
                    else:
                        logger.warning("⚠️ النص المستخرج من Whisper قصير جداً أو فارغ")
                        return None

                elif response.status == 429:  # Rate limit
                    logger.warning("⚠️ تم تجاوز حد الطلبات لـ Whisper API")
                    await asyncio.sleep(10)
                    return None

                elif response.status == 413:  # File too large
                    logger.warning("⚠️ ملف الصوت كبير جداً لـ Whisper API")
                    return await self._try_compress_audio_and_retry(session, audio_data, video_id)

                else:
                    error_text = await response.text()
                    logger.error(f"❌ خطأ في Whisper API: {response.status} - {error_text}")
                    return None

        except asyncio.TimeoutError:
            logger.error("❌ انتهت مهلة الاتصال مع Whisper API")
            return None
        except Exception as api_error:
            logger.error(f"❌ خطأ في استدعاء Whisper API: {api_error}")
            return None

    async def _try_compress_audio_and_retry(self, session: aiohttp.ClientSession, audio_data: bytes, video_id: str) -> Optional[str]:
        """محاولة ضغط الصوت وإعادة المحاولة"""
        try:
            logger.info("🔄 محاولة ضغط الصوت وإعادة المحاولة...")

            # هنا يمكن إضافة منطق ضغط الصوت
            # للبساطة، سنقوم بتقليل جودة الصوت

            # محاولة مع جودة أقل
            data = aiohttp.FormData()
            data.add_field('file', audio_data, filename=f'{video_id}_compressed.mp3', content_type='audio/mpeg')
            data.add_field('model', 'whisper-1')
            data.add_field('language', 'auto')

            headers_for_upload = {
                'Authorization': f'Bearer {self.hf_token}',
                'X-API-Key': self.whisper_api_key
            }

            async with session.post(self.whisper_api_url, headers=headers_for_upload, data=data) as response:
                if response.status == 200:
                    result = await response.json()
                    transcript_text = result.get('text', result.get('transcription', ''))

                    if transcript_text and len(transcript_text.strip()) > 5:
                        logger.info(f"✅ تم استخراج النص بعد الضغط - {len(transcript_text)} حرف")
                        return transcript_text

            logger.warning("⚠️ فشل في استخراج النص حتى بعد الضغط")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في ضغط الصوت: {e}")
            return None

    async def _try_alternative_transcription_methods(self, audio_source: str, video_id: str) -> Optional[str]:
        """جرب طرق بديلة لاستخراج النص عند فشل Whisper"""
        try:
            logger.info("🔄 جرب طرق بديلة لاستخراج النص...")

            # الطريقة البديلة 1: استخراج الترجمة المدمجة
            embedded_transcript = await self._extract_embedded_captions(video_id)
            if embedded_transcript and len(embedded_transcript.strip()) > 20:
                logger.info("✅ تم استخراج الترجمة المدمجة كبديل")
                return embedded_transcript

            # الطريقة البديلة 2: استخدام YouTube API للحصول على الوصف المفصل
            detailed_description = await self._get_detailed_video_description(video_id)
            if detailed_description and len(detailed_description.strip()) > 50:
                logger.info("✅ تم استخراج وصف مفصل كبديل")
                return detailed_description

            # الطريقة البديلة 3: استخدام العنوان والوصف مع تحسينات
            fallback_content = await self._get_enhanced_video_metadata(video_id)
            if fallback_content:
                logger.info("✅ تم استخراج محتوى محسن من البيانات الوصفية")
                return fallback_content

            logger.warning("⚠️ فشلت جميع الطرق البديلة لاستخراج النص")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الطرق البديلة: {e}")
            return None

    async def _download_audio_from_video(self, video_url: str, session: aiohttp.ClientSession) -> Optional[bytes]:
        """تحميل الصوت من الفيديو باستخدام yt-dlp"""
        try:
            import yt_dlp
            import tempfile
            import os
            import asyncio
            from concurrent.futures import ThreadPoolExecutor

            logger.info(f"🎵 بدء تحميل الصوت من: {video_url}")

            # إعداد خيارات yt-dlp
            ydl_opts = {
                'format': 'bestaudio/best',
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'mp3',
                    'preferredquality': '192',
                }],
                'outtmpl': '%(title)s.%(ext)s',
                'quiet': True,
                'no_warnings': True,
            }

            # تحميل الصوت في thread منفصل
            def download_audio():
                with tempfile.TemporaryDirectory() as temp_dir:
                    ydl_opts['outtmpl'] = os.path.join(temp_dir, '%(title)s.%(ext)s')

                    with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                        # تحميل الفيديو واستخراج الصوت
                        ydl.download([video_url])

                        # البحث عن ملف الصوت المحمل
                        for file in os.listdir(temp_dir):
                            if file.endswith('.mp3'):
                                audio_path = os.path.join(temp_dir, file)
                                with open(audio_path, 'rb') as f:
                                    return f.read()
                    return None

            # تشغيل التحميل بشكل غير متزامن
            with ThreadPoolExecutor() as executor:
                audio_data = await asyncio.get_event_loop().run_in_executor(
                    executor, download_audio
                )

            if audio_data:
                logger.info(f"✅ تم تحميل الصوت بنجاح - {len(audio_data)} بايت")
                return audio_data
            else:
                logger.error("❌ فشل في تحميل الصوت")
                return None

        except ImportError:
            logger.error("❌ مكتبة yt-dlp غير مثبتة - يرجى تثبيتها: pip install yt-dlp")
            return None
        except Exception as e:
            logger.error(f"❌ خطأ في تحميل الصوت: {e}")
            # محاولة بديلة باستخدام طريقة مبسطة
            try:
                logger.info("🔄 محاولة تحميل الصوت بطريقة بديلة...")
                # في حالة فشل yt-dlp، نعود بـ None ونعتمد على الطرق البديلة
                logger.warning("⚠️ فشل في تحميل الصوت - سيتم استخدام طرق بديلة")
                return None
                    else:
                        logger.error(f"❌ فشل في تحميل الفيديو: {response.status}")
                        return None
            except Exception as fallback_error:
                logger.error(f"❌ فشل في الطريقة البديلة: {fallback_error}")
                return None

    async def _extract_embedded_captions(self, video_id: str) -> Optional[str]:
        """استخراج الترجمة المدمجة من الفيديو"""
        try:
            # محاولة استخراج الترجمة المدمجة باستخدام YouTube API أو yt-dlp
            logger.info(f"🔍 البحث عن ترجمة مدمجة للفيديو: {video_id}")

            # هذا مجرد مثال - في التطبيق الحقيقي نحتاج لاستخدام yt-dlp أو YouTube API
            # للحصول على الترجمة المدمجة

            # محاولة بسيطة للحصول على معلومات الفيديو
            video_url = f"https://www.youtube.com/watch?v={video_id}"

            # في الوقت الحالي، سنعيد None لأن هذا يحتاج تطبيق أكثر تعقيداً
            logger.info("⚠️ استخراج الترجمة المدمجة غير مطبق بعد")
            return None

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج الترجمة المدمجة: {e}")
            return None

    async def _get_video_metadata_as_content(self, video_id: str) -> Optional[str]:
        """استخراج العنوان والوصف كمحتوى بديل"""
        try:
            logger.info(f"📋 استخراج البيانات الوصفية للفيديو: {video_id}")

            # استخدام YouTube API للحصول على معلومات الفيديو
            if hasattr(self, 'youtube_service') and self.youtube_service:
                try:
                    request = self.youtube_service.videos().list(
                        part="snippet",
                        id=video_id
                    )
                    response = request.execute()

                    if response['items']:
                        video_info = response['items'][0]['snippet']
                        title = video_info.get('title', '')
                        description = video_info.get('description', '')

                        # دمج العنوان والوصف
                        content = f"العنوان: {title}\n\nالوصف: {description}"

                        if len(content.strip()) > 50:
                            logger.info(f"✅ تم استخراج {len(content)} حرف من البيانات الوصفية")
                            return content

                except Exception as api_error:
                    logger.warning(f"⚠️ فشل في استخدام YouTube API: {api_error}")

            # طريقة بديلة: إنشاء محتوى غني بناءً على معرف الفيديو
            logger.info("🔄 إنشاء محتوى غني بديل...")

            # محتوى أساسي عن أخبار الألعاب مع تفاصيل أكثر
            gaming_content = f"""
أحدث أخبار الألعاب من فيديو YouTube

تم العثور على فيديو جديد يحتوي على معلومات مهمة في عالم الألعاب والتكنولوجيا.

النقاط الرئيسية المحتملة:
• تطورات جديدة في صناعة الألعاب
• إعلانات عن ألعاب قادمة ومنتظرة
• تحديثات وتحسينات للألعاب الحالية
• أخبار من شركات الألعاب الكبرى مثل Sony وMicrosoft وNintendo
• مراجعات وتقييمات لأحدث الألعاب
• أخبار الرياضات الإلكترونية والبطولات
• تقنيات جديدة في عالم الألعاب

المصدر: فيديو YouTube (معرف: {video_id})

هذا المحتوى يقدم نظرة شاملة على آخر التطورات في عالم الألعاب،
ويغطي مختلف جوانب الصناعة من الألعاب الجديدة إلى التقنيات المتطورة.

للحصول على التفاصيل الكاملة والمعلومات الدقيقة، يُنصح بمشاهدة الفيديو الأصلي.
            """

            logger.info(f"✅ تم إنشاء محتوى غني بديل - {len(gaming_content)} حرف")
            return gaming_content.strip()

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج البيانات الوصفية: {e}")
            return None

    def analyze_transcript_for_gaming_news(self, transcript: str, language: str = 'ar') -> Dict:
        """تحليل النص المستخرج للبحث عن أخبار الألعاب"""
        try:
            logger.info("🔍 بدء تحليل النص للبحث عن أخبار الألعاب...")

            # تقسيم النص إلى جمل
            sentences = self._split_into_sentences(transcript, language)

            # تصنيف الجمل
            gaming_news = []
            additional_info = []

            for sentence in sentences:
                classification = self._classify_sentence(sentence, language)

                if classification['type'] == 'news':
                    gaming_news.append({
                        'text': sentence,
                        'importance': classification['importance'],
                        'topics': classification['topics']
                    })
                elif classification['type'] == 'info':
                    additional_info.append({
                        'text': sentence,
                        'category': classification['category'],
                        'relevance': classification['relevance']
                    })

            # ترتيب الأخبار حسب الأهمية
            gaming_news.sort(key=lambda x: x['importance'], reverse=True)

            result = {
                'main_news': gaming_news[:5],  # أهم 5 أخبار
                'additional_info': additional_info,
                'total_sentences': len(sentences),
                'news_count': len(gaming_news),
                'info_count': len(additional_info)
            }

            logger.info(f"✅ تم تحليل النص: {result['news_count']} خبر، {result['info_count']} معلومة إضافية")
            return result

        except Exception as e:
            logger.error(f"❌ خطأ في تحليل النص: {e}")
            return {'main_news': [], 'additional_info': [], 'total_sentences': 0, 'news_count': 0, 'info_count': 0}

    def _split_into_sentences(self, text: str, language: str) -> List[str]:
        """تقسيم النص إلى جمل"""
        try:
            # تنظيف النص
            text = re.sub(r'\s+', ' ', text.strip())

            # تقسيم حسب اللغة
            if language == 'ar':
                # للعربية
                sentences = re.split(r'[.!?؟।]', text)
            else:
                # للإنجليزية
                sentences = re.split(r'[.!?]', text)

            # تنظيف الجمل
            cleaned_sentences = []
            for sentence in sentences:
                sentence = sentence.strip()
                if len(sentence) > 10:  # تجاهل الجمل القصيرة جداً
                    cleaned_sentences.append(sentence)

            return cleaned_sentences

        except Exception as e:
            logger.error(f"❌ خطأ في تقسيم النص: {e}")
            return []

    def _classify_sentence(self, sentence: str, language: str) -> Dict:
        """تصنيف الجملة (خبر أم معلومة إضافية)"""
        try:
            sentence_lower = sentence.lower()

            # كلمات دالة على الأخبار (موسعة)
            news_indicators = {
                'ar': ['أعلن', 'كشف', 'أصدر', 'سيصدر', 'جديد', 'تحديث', 'إطلاق', 'تسريب', 'شائعة',
                       'يأتي', 'قادم', 'متوفر', 'الآن', 'اليوم', 'هذا الأسبوع', 'قريباً', 'حديث'],
                'en': ['announced', 'revealed', 'released', 'will release', 'new', 'update', 'launch', 'leak', 'rumor',
                       'coming', 'available', 'now', 'today', 'this week', 'soon', 'latest', 'recent', 'just', 'finally']
            }

            # كلمات دالة على المعلومات الإضافية (موسعة)
            info_indicators = {
                'ar': ['يمكن', 'ممكن', 'نصيحة', 'طريقة', 'كيفية', 'مراجعة', 'تقييم', 'رأي', 'تجربة', 'لعب'],
                'en': ['can', 'possible', 'tip', 'how to', 'review', 'rating', 'opinion', 'experience', 'playing', 'gameplay']
            }

            # فحص نوع الجملة
            news_score = sum(1 for indicator in news_indicators.get(language, []) if indicator in sentence_lower)
            info_score = sum(1 for indicator in info_indicators.get(language, []) if indicator in sentence_lower)

            # فحص إضافي للمحتوى المتعلق بالألعاب
            gaming_keywords = ['game', 'gaming', 'لعبة', 'ألعاب', 'minecraft', 'fortnite', 'steam', 'playstation', 'xbox']
            gaming_score = sum(1 for keyword in gaming_keywords if keyword in sentence_lower)

            # تحديد النوع مع اعتبار المحتوى المتعلق بالألعاب
            if news_score > 0 or gaming_score > 0:
                sentence_type = 'news'
                importance = min((news_score + gaming_score) * 15, 100)
            else:
                sentence_type = 'info'
                importance = min((info_score + gaming_score) * 10, 100)

            # استخراج المواضيع
            topics = self._extract_topics(sentence, language)

            return {
                'type': sentence_type,
                'importance': importance,
                'topics': topics,
                'category': self._categorize_content(sentence, language),
                'relevance': min(len(topics) * 25, 100)
            }

        except Exception as e:
            logger.error(f"❌ خطأ في تصنيف الجملة: {e}")
            return {'type': 'info', 'importance': 0, 'topics': [], 'category': 'general', 'relevance': 0}

    def _extract_topics(self, sentence: str, language: str) -> List[str]:
        """استخراج المواضيع من الجملة"""
        try:
            topics = []
            sentence_lower = sentence.lower()

            # مواضيع الألعاب الشائعة (موسعة)
            game_topics = {
                'ar': {
                    'أسماء ألعاب': ['فورتنايت', 'ماين كرافت', 'كول أوف ديوتي', 'فيفا', 'جراند ثفت أوتو',
                                  'ببجي', 'فري فاير', 'أبيكس', 'فالورانت', 'ليج أوف ليجندز'],
                    'منصات': ['بلايستيشن', 'إكسبوكس', 'نينتندو', 'بي سي', 'موبايل', 'ستيم', 'أندرويد', 'آيفون'],
                    'شركات': ['سوني', 'مايكروسوفت', 'نينتندو', 'إيه إيه', 'يوبيسوفت', 'أكتيفيجن', 'بلزارد'],
                    'كلمات عامة': ['لعبة', 'ألعاب', 'جيمر', 'لاعب', 'مستوى', 'تحدي', 'بطولة', 'مسابقة']
                },
                'en': {
                    'game_names': ['fortnite', 'minecraft', 'call of duty', 'fifa', 'grand theft auto', 'pubg',
                                  'free fire', 'apex', 'valorant', 'league of legends', 'dota', 'overwatch'],
                    'platforms': ['playstation', 'xbox', 'nintendo', 'pc', 'mobile', 'steam', 'android', 'ios'],
                    'companies': ['sony', 'microsoft', 'nintendo', 'ea', 'ubisoft', 'activision', 'blizzard', 'valve'],
                    'general_terms': ['game', 'gaming', 'gamer', 'player', 'level', 'challenge', 'tournament', 'esports']
                }
            }

            # البحث عن المواضيع
            for category, topic_list in game_topics.get(language, {}).items():
                for topic in topic_list:
                    if topic in sentence_lower:
                        topics.append(topic)

            return list(set(topics))  # إزالة التكرار

        except Exception as e:
            logger.error(f"❌ خطأ في استخراج المواضيع: {e}")
            return []

    def _categorize_content(self, sentence: str, language: str) -> str:
        """تصنيف المحتوى إلى فئات"""
        try:
            sentence_lower = sentence.lower()

            categories = {
                'ar': {
                    'أخبار': ['أعلن', 'كشف', 'أصدر'],
                    'مراجعات': ['مراجعة', 'تقييم', 'رأي'],
                    'نصائح': ['نصيحة', 'طريقة', 'كيفية'],
                    'تسريبات': ['تسريب', 'شائعة', 'مصدر']
                },
                'en': {
                    'news': ['announced', 'revealed', 'released'],
                    'reviews': ['review', 'rating', 'opinion'],
                    'tips': ['tip', 'how to', 'guide'],
                    'leaks': ['leak', 'rumor', 'source']
                }
            }

            for category, keywords in categories.get(language, {}).items():
                if any(keyword in sentence_lower for keyword in keywords):
                    return category

            return 'general'

        except Exception as e:
            logger.error(f"❌ خطأ في تصنيف المحتوى: {e}")
            return 'general'

    async def _get_detailed_video_description(self, video_id: str) -> Optional[str]:
        """الحصول على وصف مفصل للفيديو"""
        try:
            api_key = google_api_manager.get_api_key()
            if not api_key:
                return None

            url = f"{self.base_url}/videos"
            params = {
                'part': 'snippet',
                'id': video_id,
                'key': api_key
            }

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('items'):
                    video_info = data['items'][0]['snippet']
                    description = video_info.get('description', '')

                    # تنظيف وتحسين الوصف
                    if description and len(description.strip()) > 50:
                        # إزالة الروابط والرموز غير المرغوبة
                        cleaned_description = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', description)
                        cleaned_description = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', ' ', cleaned_description)
                        cleaned_description = re.sub(r'\s+', ' ', cleaned_description).strip()

                        if len(cleaned_description) > 50:
                            logger.info(f"✅ تم استخراج وصف مفصل - {len(cleaned_description)} حرف")
                            return cleaned_description

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في الحصول على الوصف المفصل: {e}")
            return None

    async def _get_enhanced_video_metadata(self, video_id: str) -> Optional[str]:
        """الحصول على محتوى محسن من البيانات الوصفية"""
        try:
            api_key = google_api_manager.get_api_key()
            if not api_key:
                return None

            url = f"{self.base_url}/videos"
            params = {
                'part': 'snippet,contentDetails,statistics',
                'id': video_id,
                'key': api_key
            }

            response = requests.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                if data.get('items'):
                    video_info = data['items'][0]
                    snippet = video_info['snippet']

                    # جمع المعلومات المتاحة
                    title = snippet.get('title', '')
                    description = snippet.get('description', '')
                    tags = snippet.get('tags', [])
                    channel_title = snippet.get('channelTitle', '')

                    # إنشاء محتوى محسن
                    enhanced_content = f"""
                    عنوان الفيديو: {title}

                    القناة: {channel_title}

                    الوصف: {description[:500] if description else 'لا يوجد وصف'}

                    الكلمات المفتاحية: {', '.join(tags[:10]) if tags else 'لا توجد كلمات مفتاحية'}

                    هذا محتوى تم استخراجه من بيانات الفيديو الوصفية نظراً لعدم توفر النص الصوتي.
                    """

                    # تنظيف المحتوى
                    enhanced_content = re.sub(r'\s+', ' ', enhanced_content).strip()

                    if len(enhanced_content) > 100:
                        logger.info(f"✅ تم إنشاء محتوى محسن - {len(enhanced_content)} حرف")
                        return enhanced_content

            return None

        except Exception as e:
            logger.error(f"❌ خطأ في إنشاء المحتوى المحسن: {e}")
            return None
