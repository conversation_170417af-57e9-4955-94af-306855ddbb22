#!/usr/bin/env python3
"""
اختبار إرسال رسالة للمدير
"""

import asyncio
from telegram import Bo<PERSON>
from config.settings import BotConfig

async def test_admin_chat():
    """اختبار إرسال رسالة للمدير"""
    try:
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        
        admin_id = BotConfig.TELEGRAM_ADMIN_ID
        print(f"🧪 اختبار إرسال رسالة للمدير: {admin_id}")
        
        # محاولة إرسال رسالة اختبار
        test_message = "🧪 رسالة اختبار من وكيل أخبار الألعاب\n\nإذا وصلتك هذه الرسالة، فإن نظام الموافقة يعمل بشكل صحيح! ✅"
        
        await bot.send_message(
            chat_id=admin_id,
            text=test_message
        )
        
        print("✅ تم إرسال رسالة الاختبار بنجاح!")
        print("📱 تحقق من تيليجرام لرؤية الرسالة")
        
    except Exception as e:
        print(f"❌ فشل في إرسال رسالة الاختبار: {e}")
        
        if "Chat not found" in str(e):
            print("\n💡 لحل هذه المشكلة:")
            print("1. تأكد من أن المدير أرسل رسالة للبوت أولاً")
            print("2. شغل: python get_admin_id.py للحصول على المعرف الصحيح")
            print("3. حدث TELEGRAM_ADMIN_ID في config/settings.py")
        elif "Unauthorized" in str(e):
            print("\n💡 مشكلة في توكن البوت:")
            print("1. تحقق من صحة TELEGRAM_BOT_TOKEN")
            print("2. تأكد من أن البوت مفعل")

if __name__ == "__main__":
    asyncio.run(test_admin_chat())
