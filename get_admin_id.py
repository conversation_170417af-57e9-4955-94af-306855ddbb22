#!/usr/bin/env python3
"""
سكريبت للحصول على معرف المدير الرقمي من تيليجرام
"""

import asyncio
from telegram import Bo<PERSON>
from config.settings import BotConfig

async def get_admin_id():
    """الحصول على معرف المدير"""
    try:
        # إنشاء البوت
        bot = Bot(token=BotConfig.TELEGRAM_BOT_TOKEN)
        
        print("🤖 بوت تيليجرام جاهز!")
        print("📱 لمعرفة معرف المدير:")
        print("1. اذهب إلى البوت في تيليجرام")
        print("2. أرسل أي رسالة للبوت")
        print("3. سيظهر معرف المدير هنا")
        print("=" * 50)
        
        # الحصول على التحديثات
        updates = await bot.get_updates()
        
        if updates:
            for update in updates[-5:]:  # آخر 5 رسائل
                if update.message:
                    chat_id = update.message.chat_id
                    username = update.message.from_user.username
                    first_name = update.message.from_user.first_name
                    
                    print(f"💬 رسالة من:")
                    print(f"   📱 معرف المحادثة: {chat_id}")
                    print(f"   👤 اسم المستخدم: @{username}")
                    print(f"   📝 الاسم الأول: {first_name}")
                    print(f"   📄 النص: {update.message.text}")
                    print("-" * 30)
        else:
            print("⚠️ لا توجد رسائل حديثة")
            print("💡 أرسل رسالة للبوت أولاً ثم شغل هذا السكريبت مرة أخرى")
            
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    asyncio.run(get_admin_id())
